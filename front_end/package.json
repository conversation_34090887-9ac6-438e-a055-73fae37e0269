{"name": "tg-download", "version": "0.0.1", "description": "TigerGraph download page", "productName": "TigerGraph Download", "author": "anyuan.zheng", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build", "dev-build": "NODE_ENV=development quasar build"}, "dependencies": {"@quasar/extras": "^1.16.4", "axios": "^1.2.1", "compare-versions": "^6.1.0", "quasar": "^2.6.0", "vue": "^3.0.0", "vue-router": "^4.0.0"}, "devDependencies": {"@quasar/app-vite": "^1.3.0", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.10.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^4.5.4"}, "engines": {"node": "^18 || ^16 || ^14.19", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}