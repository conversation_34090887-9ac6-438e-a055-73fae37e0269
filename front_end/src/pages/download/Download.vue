<template>
  <q-layout>
    <q-page-container>
      <q-page class="download-page">
        <header>
          <div class="container">
            <div class="logo">
              <a
                href="https://www.tigergraph.com/"
                class="custom-logo-link"
                rel="home"
                aria-current="page"
              ><img src="~assets/images/tg_logo.png"></a>
            </div>
          </div>
        </header>
        <div class="container">
          <div class="head_section">
            <div class="head_content">
              <p>Unleash the power of interconnected data for deeper insights and better outcomes with Native Parallel Graph™ technology</p>
              <form
                method="get"
                id="form_latest"
                action=""
              >
                <a
                  href="javascript:;"
                  @click="onDownloadLatest"
                >DOWNLOAD LATEST LTS VERSION <q-icon
                    name="expand_more"
                    size="19px"
                  /></a>
              </form>

            </div>
            <div class="head_content_img">
              <img src="~assets/images/tg_load.png">
            </div>
          </div>
        </div>


        <section class="tab_section">
          <div class="tab_head">
            <h1>Current Releases</h1>
            <hr>
          </div>
          <div class="container">
            <div class="tab">
              <button
                class="tablinks"
                @click="tabhead($event, 'Enterprise')"
                id="tab_Enterprise"
              >Enterprise Edition<q-icon
                  name="expand_more"
                  size="19px"
                /></button>
              <button
                class="tablinks"
                @click="tabhead($event, 'Community')"
                id="tab_Community"
              >Community Edition <q-icon
                  name="expand_more"
                  size="19px"
                /></button>
              <button
                class="tablinks"
                @click="tabhead($event, 'Tools')"
                id="tab_Tools"
              >Tools <q-icon
                  name="expand_more"
                  size="19px"
                /></button>
              <button
                class="tablinks"
                @click="tabhead($event, 'Solution')"
                id="tab_Solution"
              >Solution <q-icon
                  name="expand_more"
                  size="19px"
                /></button>
            </div>
          </div>

          <!---Enterprise-->
          <div
            id="Enterprise"
            class="tabcontent"
          >
            <div
              v-if="packages.length === 0"
              class="loading"
            >
              <q-spinner
                size="100px"
                color="primary"
              />
            </div>
            <div v-else>
              <br><span class="inner-text">Our PGP public key ID: <b>413D1F12</b> with fingerprint: <b>E20D 2B61 FB38 57D4 3B8E B321 67BD 323E 413D 1F12</b></span>
              <div v-if="getEnterprisePreviewPkgs().length > 0">
                <h1 class="inner-title">Preview Version</h1><span class="inner-text">First look at new features. Not for production use.</span>
                <download-tab
                  :pkgs="getEnterprisePreviewPkgs()"
                  :edition="Edition.Enterprise"
                  @download="onDownload"
                />
              </div>
              <h1 class="inner-title">Latest LTS Version</h1><span class="inner-text">Long term support. Recommended for production use cases.</span>
              <download-tab
                :pkgs="getEnterpriseLTSPkgs()"
                :edition="Edition.Enterprise"
                @download="onDownload"
              />
            </div>
          </div>

          <div
            id="Community"
            class="tabcontent"
          >
            <div
              v-if="packages.length === 0"
              class="loading"
            >
              <q-spinner
                size="100px"
                color="primary"
              />
            </div>
            <div v-else>
              <!-- <h3>Docker image is available directly from Docker Hub: <span style="background-color:#27343b;color:#ffffff;font-weight:normal;"> docker pull tigergraph/tigergraph:latest </span><br> Please visit <a
                  href="https://github.com/tigergraph/ecosys/blob/master/demos/guru_scripts/docker/README.md"
                  target="_blank"
                  rel="noopener"
                ><span style="color: #f78117;">TigerGraph Ecosystem<span></span></span></a> for detailed instructions.</h3> -->
              <download-tab
                :pkgs="getCommunityPkgs()"
                :edition="Edition.Community"
                @download="onDownload"
              />
            </div>
          </div>

          <!---Tools-->
          <div
            id="Tools"
            class="tabcontent"
          >
            <div
              v-if="packages.length === 0"
              class="loading"
            >
              <q-spinner
                size="100px"
                color="primary"
              />
            </div>
            <tools-tab
              v-else
              :pkgs="toolsPkgs"
              :type="'Tools'"
              @download="onDownload"
            />
          </div>

          <!---Solution-->
          <div
            id="Solution"
            class="tabcontent"
          >
            <div
              v-if="solutions.length === 0"
              class="loading"
            >
              <q-spinner
                size="100px"
                color="primary"
              />
            </div>
            <solution-tab
              v-else
              :solutions="solutions"
              @download="onSolutionDownload"
            />
          </div>

        </section>


        <!-- Documentation and Tutorials Section -->
        <section class="docs-tutorials-section">
          <div class="container">
            <div class="section-content">
              <div class="docs-block">
                <h2>Documentation & Quick Start Guide</h2>

                <p>See our <a
                    href="https://docs.tigergraph.com/tigergraph-server/current/getting-started/"
                    target="_blank"
                    rel="noopener"
                    class="docs-link"
                  >Getting Started</a> guides for help installing and learning to use TigerGraph.</p>

                <p>Learn how to install and configure TigerGraph using Docker. <a
                  href="https://github.com/tigergraph/ecosys/blob/master/demos/guru_scripts/docker/README.md"
                  target="_blank"
                  rel="noopener"
                  class="docs-link"
                >[Read the Docs]</a></p>

                <p>Interested in the release with our latest features? You'll find it at <a href="https://savanna.tgcloud.io" class="docs-link">TigerGraph Savanna</a>.</p>
              </div>

              <div class="tutorials-block">
                <h2>Tutorials & Learning Resources</h2>
                <ul class="tutorial-list">
                  <li>
                    <a
                      href="https://github.com/tigergraph/ecosys/blob/master/tutorials/GSQL.md"
                      target="_blank"
                      rel="noopener"
                      class="tutorial-link"
                    >Graph Query Language Tutorial</a>
                    <p>– Learn how to write powerful queries in TigerGraph.</p>
                  </li>
                  <li>
                    <a
                      href="https://github.com/tigergraph/ecosys/blob/master/tutorials/Cypher.md"
                      target="_blank"
                      rel="noopener"
                      class="tutorial-link"
                    >OpenCypher Tutorial</a>
                    <p>– Learn how to write OpenCypher queries in TigerGraph.</p>
                  </li>
                  <li>
                    <a
                      href="https://github.com/tigergraph/ecosys/blob/master/tutorials/VectorSearch.md"
                      target="_blank"
                      rel="noopener"
                      class="tutorial-link"
                    >Vector Search Tutorial</a>
                    <p>– Learn how to integrate vector search with graph queries for AI-powered applications.</p>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <download-request-form
          ref="myRef"
          :package-info="packageInfo"
        />
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script lang="ts">
import DownloadRequestForm from './DownloadRequestForm.vue';
import { defineComponent } from 'vue';
import DownloadTab from './DownloadTab.vue';
import ToolsTab from './ToolsTab.vue';
import SolutionTab from './SolutionTab.vue';
import { PackageType, Package, Edition } from './packages';

export interface Solution {
  name: string;
  description: string;
  path: string;
  categories: string[];
  icon?: string;
  is_library?: boolean;
}
import { api } from '../../boot/axios';
import { useQuasar } from 'quasar';
import solutionList from '../../data/solution/solution_list.json';

export default defineComponent({
  components: { DownloadRequestForm, DownloadTab, ToolsTab, SolutionTab },
  name: 'DownloadComponent',
  setup() {
    const Q = useQuasar();
    return {
      Q,
    };
  },
  data() {
    return {
      isFormShow: false,
      packageInfo: {
        type: '',
        version: '',
        file_name: '',
        edition: '',
        solution_name: '',
      },
      packages: [] as Package[],
      solutions: solutionList.filter(solution => !solution.is_library),
    };
  },
  async created() {
    try {
      const res = await api.get<Package[]>(process.env.API_URL + '/download');
      this.packages = res.data;
    } catch (error) {
      this.Q.notify({
        message: 'Failed to get download info.',
        color: 'negative',
      });
      console.warn('Error fetching release info:', error);
    }


  },
  computed: {
    Edition() {
      return Edition;
    },
    toolsPkgs(): Package[] {
      return this.packages
        .filter((pkg) => pkg.type === PackageType.Tools)
        .sort((a, b) => {
          if (b.link.toLowerCase().includes('latest')) {
            return 1;
          }
          if (a.link.toLocaleLowerCase().includes('latest')) {
            return -1;
          }
          return (
            new Date(b.endOfSupport).getTime() -
            new Date(a.endOfSupport).getTime()
          );
        });
    },
  },
  mounted() {
    let anchorHash = window.location.href.toString();
    if (anchorHash.lastIndexOf('#') != -1) {
      anchorHash = 'tab_' + anchorHash.substr(anchorHash.lastIndexOf('#') + 1);
      const anchorElem = document.getElementById(anchorHash);
      if (anchorElem) {
        anchorElem.click();
      } else {
        const tab = document.getElementById('tab_Enterprise');
        if (tab) {
          tab.click();
        }
      }
    } else {
      // Get the element with id="defaultOpen" and click on it
      const tab = document.getElementById('tab_Community');
      if (tab) {
        tab.click();
      }
    }
  },
  methods: {
    getCommunityPkgs(): Package[] {
      return this.packages.filter(
        (pkg) =>
          pkg.edition.includes(Edition.Community) && pkg.type !== PackageType.Tools
      );
    },
    getEnterpriseLTSPkgs(): Package[] {
      return this.packages.filter(
        (pkg) =>
          pkg.edition.includes(Edition.Enterprise) &&
          !this.isPreviewVersion(pkg) &&
          pkg.type !== PackageType.Tools
      );
    },
    getEnterprisePreviewPkgs(): Package[] {
      return this.packages.filter(
        (pkg) =>
          pkg.edition.includes(Edition.Enterprise) &&
          this.isPreviewVersion(pkg) &&
          pkg.type !== PackageType.Tools
      );
    },
    onDownloadLicense() {
      this.packageInfo.type = 'License';
      this.packageInfo.version = '';
      // eslint-disable-next-line
      (this.$refs.myRef as any).show();
    },
    onDownloadLatest() {
      this.packageInfo.type = 'Enterprise';
      this.packageInfo.version = this.getEnterpriseLTSPkgs()[0].version;
      this.packageInfo.edition = Edition.Enterprise;
      // eslint-disable-next-line
      (this.$refs.myRef as any).show();
    },
    onDownload(pkg: Package) {
      this.packageInfo.type = pkg.type;
      this.packageInfo.version = pkg.version;
      this.packageInfo.file_name = pkg.link.replace(/.*\//g, '');
      if (pkg.edition.includes(Edition.Enterprise)) {
        this.packageInfo.edition = Edition.Enterprise;
      } else if (pkg.edition.includes(Edition.Community)) {
        this.packageInfo.edition = Edition.Community;
      } else {
        this.packageInfo.edition = pkg.edition;
      }

      // eslint-disable-next-line
      (this.$refs.myRef as any).show();
    },
    onSolutionDownload(solution: Solution) {
      this.packageInfo.type = 'solution';
      this.packageInfo.version = '';
      this.packageInfo.file_name = solution.path;
      this.packageInfo.edition = '';
      this.packageInfo.solution_name = solution.name;

      // eslint-disable-next-line
      (this.$refs.myRef as any).show();
    },
    tabhead(evt: MouseEvent, cityName: string) {
      let i, tablinks;
      let tabcontent = document.getElementsByClassName('tabcontent');
      for (i = 0; i < tabcontent.length; i++) {
        let content = tabcontent[i] as HTMLElement;
        content.style.display = 'none';
      }
      tablinks = document.getElementsByClassName('tablinks');
      for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(' active', '');
      }
      const tab = document.getElementById(cityName);
      if (tab) {
        tab.style.display = 'block';
      }
      (evt.currentTarget as HTMLElement).className += ' active';
    },
    isPreviewVersion(pkg: Package) {
      return pkg.edition.toLowerCase().includes('preview');
    },
  },
});
</script>

<style lang="scss">
@import url('https://use.typekit.net/nzi0hja.css');
body {
  padding: 0;
  margin: 0;
}
.loading {
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.download-page {
  .container {
    max-width: 1240px;
    margin: 0 auto;
  }
  .head_section {
    margin-top: 25px;
    display: flex;
    align-items: center;
  }
  .head_content {
    width: 60%;
    padding-left: 80px;
  }
  .head_content_img {
    width: 40%;
    padding-right: 80px;
  }
  .head_text {
    margin-top: 28px;
  }
  .tab {
    overflow: hidden;
    display: flex;
  }

  /* Style the buttons inside the tab */
  .tab button {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 11px 16px;
    transition: 0.3s;
    font-size: 20px;
    line-height: 35px;
    width: 100%;
    color: #929292;
    font-family: urbane;
  }

  /* Create an active/current tablink class */
  .tab button.active {
    background-color: #f78117;
    color: #fff;
  }

  /* Style the tab content */
  .tabcontent {
    display: block;
    padding: 0px;
    max-width: 1240px;
    margin: 0 auto;
  }
  .inner-tab table {
    width: 100%;
  }
  .inner-tab thead {
    background-color: #ddd;
  }
  a {
    text-decoration: none;
  }
  table a {
    color: #1e90ff;
  }
  table a:hover {
    color: #f78117;
  }
  .head_content p {
    font-size: 28px;
    line-height: 36px;
    font-family: urbane;
  }
  .head_content form a {
    font-size: 16px;
    font-weight: 600;
    line-height: 21px;
    letter-spacing: 0.4px;
    background-color: rgba(255, 255, 255, 0);
    color: #000000;
    border-style: solid;
    border-width: 2px 2px 2px 2px;
    border-radius: 0px 0px 0px 0px;
    padding: 10px 10px 10px 10px;
    border-color: #f78117;
    font-family: urbane;
  }
  .head_content form a:hover {
    background-color: #f78117;
    color: #fff;
  }
  .tab_head h1 {
    font-size: 35px;
    line-height: 45px;
    margin: 0;
    font-family: urbane;
    padding-bottom: 20px;
    text-align: center;
  }
  .tabcontent h1 {
    font-size: 35px;
    line-height: 45px;
    margin-bottom: 0px;
    margin-top: 28px;
    font-family: urbane;
    color: #f78117;
  }
  .tabcontent h2 {
    font-size: 22px;
    line-height: 28px;
    margin-bottom: 0px;
    margin-top: 28px;
    font-family: urbane;
    color: #4eb8d4;
  }
  .tabcontent h3,
  .container h3 {
    font-size: 16px;
    line-height: 21px;
    margin-bottom: 0px;
    margin-top: 28px;
    font-family: urbane;
  }
  td {
    font-size: 14px;
    line-height: 16px;
  }
  table,
  th,
  td {
    border-collapse: collapse;
    text-align: center;
    padding: 12px 0px;
    text-transform: uppercase;
  }
  th,
  td {
    font-family: usual;
    font-weight: 500;
    font-size: 14px;
    color: #6d6e71;
  }
  hr {
    margin: 0;
  }
  .logo {
    padding: 25px 0px;
  }
  .tab_section {
    padding-top: 55px;
    padding-bottom: 65px;
  }
  table {
    border-bottom: 1px solid #f78117;
  }
  .inner-title {
    margin-top: 30px;
    margin-bottom: 18px;
    font-family: urbane;
    font-weight: 400;
  }
  .inner-text {
    font-family: urbane;
  }
  // .tab button::after, .head_content form a::after {
  // 	content: '\f107';
  // 	font-family: fontawesome;
  // 	padding-left: 10px;
  // 	font-size: 19px;
  // 	font-weight: 800;
  // }
  @media only screen and (max-width: 1300px) and (min-width: 1024px) {
    .tabcontent,
    .container {
      width: 90%;
    }
  }
  @media only screen and (max-width: 1024px) {
    .container {
      padding: 0 15px;
    }
  }
  @media only screen and (max-width: 767px) {
    .head_section {
      display: flex;
      flex-direction: column;
      margin-top: 25px;
    }
    .head_content {
      width: 100%;
      padding: 0;
      text-align: center;
    }
    .head_content_img {
      width: 100%;
      float: left;
      padding-top: 43px;
      text-align: center;
    }
    .tab {
      flex-wrap: wrap;
    }
    table,
    th,
    td {
      text-align: left;
    }
    .tab button::after {
      display: block;
    }
    .tab button {
      width: 50%;
      border: 2px solid #b3b3b3;
      border-bottom: 4px solid #b3b3b3;
    }
    .tab button:nth-child(4) {
      width: 100%;
    }
    .inner-title {
      text-align: center;
    }
    .inner-tab table {
      width: 100%;
      display: flex;
    }
    th,
    td {
      display: flex;
    }
    th,
    td {
      padding: 12px 20px;
    }
    td {
      word-wrap: anywhere;
    }
    .tabcontent h2,
    .tabcontent h3 {
      text-align: center;
    }
    .tab button:nth-child(n + 1) {
      border-top: 0;
    }
    .tab button:nth-child(2n + 1) {
      border-left: 0;
    }
    .tab button:nth-child(2n + 2) {
      border-right: 0;
    }
    .inner-text a {
      font-size: 36px;
      font-weight: bold;
    }
    .tab_head h1 {
      font-size: 28px;
      line-height: 38px;
      margin: 0;
      padding-bottom: 23px;
    }
    .tab_section .container {
      padding: 0;
    }
  }
}

/* Documentation and Tutorials Section Styles */
.docs-tutorials-section {
  padding: 40px 0;
  background-color: #f9f9f9;
}

.section-content {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.docs-block,
.tutorials-block {
  flex: 1;
  min-width: 300px;
}

.docs-block h2,
.tutorials-block h2 {
  font-family: urbane;
  font-size: 28px;
  color: #333;
  margin-bottom: 15px;
}

.docs-block p {
  font-family: urbane;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 20px;
  color: #555;
}

.docs-link {
  display: inline-block;
  color: #1e90ff;
  font-family: urbane;
  text-decoration: underline;
  transition: color 0.3s;
}

.docs-link:hover {
  color: #f78117;
}

.tutorial-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tutorial-list li {
  margin-bottom: 20px;
}

.tutorial-link {
  display: block;
  font-family: urbane;
  font-size: 18px;
  font-weight: 500;
  color: #1e90ff;
  margin-bottom: 5px;
  transition: color 0.3s;
}

.tutorial-link:hover {
  color: #f78117;
}

.tutorial-list p {
  font-family: urbane;
  font-size: 15px;
  line-height: 1.4;
  color: #555;
  margin: 0;
  padding-left: 5px;
}

/* Media Queries for Responsive Design */
@media only screen and (max-width: 767px) {
  .section-content {
    flex-direction: column;
  }

  .docs-block,
  .tutorials-block {
    width: 100%;
  }
}
</style>
