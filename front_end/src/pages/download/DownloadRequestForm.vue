<template>
  <q-drawer
    side="right"
    :width="500"
    :visible-breakpoint="0"
    :overlay="true"
    ref="drawer"
    v-model="isShow"
    style="border-top: 1px solid rgba(0, 0, 0, 0.12);"
    bordered
  >
    <div class="q-pa-md">
      <q-btn
        flat
        round
        dense
        icon="close"
        class="q-ml-md float-right"
        @click="close"
      />
      <div>
        <h2
          class="text-h6 q-mb-md text-center"
          style="display: inline-block; width: 100%;"
        >{{ getFormTitle() }}</h2>
      </div>
      <div class="text-subtitle1 text-center">Please fill out this form to begin your {{ packageInfo.type === 'License' ? "request" : "download" }}.</div>
      <q-form
        @submit="submitForm"
        ref="form"
      >
        <q-input
          v-model="firstName"
          label="First Name"
          lazy-rules
          :rules="[requiredRule]"
        ></q-input>
        <q-input
          v-model="lastName"
          label="Last Name"
          lazy-rules
          :rules="[requiredRule]"
        ></q-input>
        <q-input
          v-model="companyName"
          label="Company Name"
          lazy-rules
          :rules="[requiredRule]"
        ></q-input>
        <q-input
          v-model="email"
          label="Business Email"
          lazy-rules
          :rules="[emailRule]"
        ></q-input>
        <q-select
          v-model="jobCategory"
          :options="jobCategoryOptions"
          label="Job Category"
          :rules="[requiredRule]"
        />
        <q-input
          v-model="phoneNumber"
          label="Phone Number"
          lazy-rules
          :rules="[requiredRule, phoneNumberRule]"
        ></q-input>
        <q-input
          v-model="linkedInProfileUrl"
          label="LinkedIn Profile URL"
          lazy-rules
        ></q-input>
        <q-select
          v-model="selectedCountry"
          :options="countryOptions"
          label="Select Country/Region"
          :rules="[requiredRule]"
          style="margin-top: 20px;"
        />
        <div class="text-decoration">By clicking the button below, you are confirming that you have read, consent and agree to TigerGraph's
          <a
            href="https://www.tigergraph.com/license-agreement/"
            target="_blank"
            style="color: #1e90ff;"
          >License Agreement</a> and
          <a
            href="https://www.tigergraph.com/privacy-policy/"
            target="_blank"
            style="color: #1e90ff;"
          >Privacy Policy.</a>
        </div>
        <div
          class="text-center"
          style="margin-top: 30px;"
        >
          <q-btn
            type="submit"
            :label="packageInfo.type === 'License' ? 'Request' : 'Download'"
            outline
            class="outlined-button text-body1 text-weight-bolder"
            :loading="requesting"
          ></q-btn>
        </div>
      </q-form>
    </div>
  </q-drawer>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { QDrawer, QForm, QInput, QSelect, QBtn, useQuasar } from 'quasar';
import countries from './countries';
import { AxiosError } from 'axios';
import { Edition } from './packages';
import { api } from '../../boot/axios';

export default defineComponent({
  setup() {
    const Q = useQuasar();
    return {
      Q,
    };
  },
  props: {
    packageInfo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isShow: false,
      drawerWidth: '30vw',
      requesting: false,
      firstName: '',
      lastName: '',
      email: '',
      jobCategory: '',
      phoneNumber: '',
      companyName: '',
      selectedCountry: { label: 'United States', value: 'US' },
      countryOptions: [] as { label: string; value: string }[],
      jobCategoryOptions: [
        'BI / Analyst',
        'Business Executive',
        'Consultant',
        'Data Engineer',
        'Data Scientist',
        'Developer / Engineer',
        'IT / DBA',
        'Product Manager',
        'Marketing',
        'Security',
        'Technical Executive',
        'Startup Founder',
        'Student',
        'Other',
      ],
      linkedInProfileUrl: '',
      requiredRule: (val: string) => !!val || 'This field is required',
      emailRule: (val: string) => {
        const emailPattern =
          /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        const excludesPattern = /gmail|outlook|hotmail|qq|163|yahoo\.com$/;
        return (
          (emailPattern.test(val) && !excludesPattern.test(val)) ||
          'Please use your company email'
        );
      },
      phoneNumberRule: (val: string) =>
        /^\+?(?:[0-9] ?){5,14}[0-9]$/.test(val) ||
        'Please enter a valid phone number',
    };
  },
  computed: {
    Edition() {
      return Edition;
    },
  },
  created() {
    this.countryOptions = countries.map((country) => ({
      label: country.countryName,
      value: country.countryShortCode,
    }));
  },
  methods: {
    show() {
      this.isShow = true;
    },
    close() {
      this.isShow = false;
    },
    getFormTitle() {
      if (this.packageInfo.type === 'solution') {
        return 'Download Solution';
      } else if (this.packageInfo.edition.includes(Edition.Enterprise)) {
        return 'Download Enterprise Package';
      } else {
        return 'Download Community Package';
      }
    },
    phoneRule(val: string) {
      const phonePattern =
        this.selectedCountry.value === 'US' ? /^\d{10}$/ : /^\d+$/;
      return phonePattern.test(val) || 'Please enter a valid phone number';
    },
    async submitForm() {
      const success = await (this.$refs.form as QForm).validate();
      if (!success) {
        return;
      }

      const user_info = {
        first_name: this.firstName,
        last_name: this.lastName,
        company_name: this.companyName,
        email: this.email,
        job_category: this.jobCategory,
        phone: this.phoneNumber,
        country: this.selectedCountry?.label,
        linkedin_profile_url: this.linkedInProfileUrl,
      };
      const package_info = this.packageInfo;

      const requestDownloadURL = process.env.API_URL + '/download';
      try {
        this.requesting = true;
        await api.post(requestDownloadURL, { user_info, package_info });
        this.close();
        this.Q.notify({
          message: 'The download links have been sent to your email.',
          color: 'positive',
        });
      } catch (error) {
        const err = error as AxiosError;
        const message = err?.response?.data as string;
        this.Q.notify({
          message: message || 'Something went wrong, please try again later.',
          color: 'negative',
        });
        console.log(error);
      }
      this.requesting = false;
    },
  },
  components: {
    QDrawer,
    QForm,
    QInput,
    QSelect,
    QBtn,
  },
});
</script>
