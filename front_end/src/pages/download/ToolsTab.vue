<template>
  <div class="inner-tab" v-for="version in versions" :key="version">
    <h2 class="inner-title">Tools for TigerGraph Enterprise Edition {{ version }}</h2>

    <table class="oew-table oew-table-even">
      <thead>
        <tr class="oew-table-row">
          <th class="oew-table-cell elementor-repeater-item-504a04b"><span class="oew-table-text"><span class="oew-table-text-inner">Release Date</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b oew-table-cell elementor-repeater-item-64611ba"><span class="oew-table-text oew-table-text"><span class="oew-table-text-inner">DOWNLOAD</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b oew-table-cell elementor-repeater-item-64611ba oew-table-cell elementor-repeater-item-d6f2d9c"><span class="oew-table-text oew-table-text oew-table-text"><span class="oew-table-text-inner">SIZE</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b oew-table-cell elementor-repeater-item-64611ba oew-table-cell elementor-repeater-item-d6f2d9c oew-table-cell elementor-repeater-item-d565d48"><span class="oew-table-text oew-table-text oew-table-text oew-table-text"><span class="oew-table-text-inner">MD5SUM</span></span></th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="oew-table-row elementor-repeater-item-ce8a89f"
          v-for="(pkg, index, ) in pkgsByVersion[version]"
          :key="pkg.type + pkg.version"
        >
          <td
            class="oew-table-cell elementor-repeater-item-707b39a"
            data-title="RELEASE"
          ><span class="oew-table-text"><span class="oew-table-text-inner">{{ pkg.endOfSupport }}</span></span></td>
          <td
            class="oew-table-cell elementor-repeater-item-de530ec"
            data-title="DOWNLOAD"
          >
            <form
              :id="`${pkg.type}-${pkg.version}-${index}`"
              method="get"
            >
              <a
                href="javascript:{}"
                @click="onDownload(pkg)"
              ><span class="oew-table-text-inner">{{ getProduct(pkg) }}<q-icon name="expand_more" /></span></a>
            </form>
          </td>
          <td
            class="oew-table-cell elementor-repeater-item-ee4b408"
            data-title="SIZE"
          ><span class="oew-table-text"><span class="oew-table-text-inner">{{ pkg.size }}</span></span></td>
          <td
            class="oew-table-cell elementor-repeater-item-dbac82a"
            data-title="Sha256Sum"
          ><span class="oew-table-text"><span class="oew-table-text-inner">{{ pkg.checksum }}</span></span></td>
        </tr>

      </tbody>
    </table>
  </div>

</template>

<script lang="ts">
import { PropType, defineComponent } from 'vue';
import { Package } from './packages';
import { compare } from 'compare-versions';

export default defineComponent({
  name: 'ToolsTab',
  props: {
    pkgs: {
      type: Object as PropType<Package[]>,
      required: true,
    },
  },
  data() {
    return {
      pkgsByVersion: {} as Record<string, Package[]>,
      versions: [] as string[],
    };
  },
  created() {
    const pkgsByVersion = this.pkgsByVersion;
    this.pkgs.forEach((pkg) => {
      if (!pkgsByVersion[pkg.version]) {
        pkgsByVersion[pkg.version] = [];
        this.versions.push(pkg.version);
      }
      pkgsByVersion[pkg.version].push(pkg);
    });
    this.versions = this.versions.sort((v1, v2) => {
      return compare(v1, v2, '>') ? -1 : 1;
    });
  },
  methods: {
    getProduct(pkg: Package) {
      return pkg.link.replace(/.*\//g, '').replace(/\.tar\.gz/g, '');
    },
    onDownload(pkg: Package) {
      this.$emit('download', pkg);
    },
  },
});
</script>

<style lang="scss" scoped>
</style>
