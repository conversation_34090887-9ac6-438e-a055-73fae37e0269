<template>
  <div class="solution-tab">
    <h2 class="inner-title">Solutions for TigerGraph 4.x </h2>
    <div class="solution-grid">
      <div
        v-for="solution in solutions"
        :key="solution.path"
        class="solution-card"
      >
        <div class="solution-icon">
          <div class="solution-icon-img-container">
            <img
              :src="getIconPath(solution.icon)"
              :alt="solution.name + ' icon'"
              class="solution-icon-img"
              @error="onIconError"
            />
          </div>

          <div class="solution-info">
            <h3 class="solution-name">{{ solution.name }}</h3>

            <div class="solution-categories">
              <q-chip
                v-for="category in solution.categories"
                :key="category"
                size="sm"
                color="rgb(234, 236, 239)"
                text-color="rgb(44, 50, 55)"
                class="category-chip"
              >
                {{ category }}
              </q-chip>
            </div>
          </div>

        </div>

        <p class="solution-description full-description">{{ solution.description }}</p>

        <div class="solution-actions">
          <img src="/icons/tigergraph.svg" alt="TigerGraph Logo" class="tg-logo">
          <q-btn
            outline
            @click="onDownload(solution)"
            class="download-btn"
          >
            Download
          </q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

import { Solution } from './Download.vue';

export default defineComponent({
  name: 'SolutionTab',
  props: {
    solutions: {
      type: Array as PropType<Solution[]>,
      required: true,
    },
  },
  methods: {
    onDownload(solution: Solution) {
      this.$emit('download', solution);
    },
    getIconPath(iconPath: string) {
      return iconPath ? `/solution/${iconPath}` : '/solution/fallback.svg';
    },
    onIconError(event: Event) {
      // Hide the image and show fallback icon if image fails to load
      const img = event.target as HTMLImageElement;
      img.style.display = 'none';
      const fallbackIcon = img.parentElement?.querySelector('.fallback-icon');
      if (fallbackIcon) {
        (fallbackIcon as HTMLElement).style.display = 'block';
      }
    },
  },
});
</script>

<style lang="scss" scoped>
.inner-title {
  margin-top: 0 !important;
  margin-bottom: 12px !important;
}
  .solution-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.solution-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 8px;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.solution-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.solution-icon {
  display: flex;
  gap: 15px;
}

.solution-icon-img-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border: 1px solid rgb(221, 221, 222);
}

.solution-icon-img {
  max-width: 48px;
  max-height: 48px;
  width: auto;
  height: auto;
}

.solution-content {
  text-align: center;
}

.solution-name {
  font-family: urbane;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  line-height: 24px;
  margin-top: 0 !important;
}

.solution-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.category-chip {
  font-size: 11px;
  height: 24px;
}

.solution-description {
  font-family: urbane;
  font-size: 14px;
  color: rgb(44, 50, 55);
  line-height: 16px;
  flex: 1;
}

.solution-description.full-description {
  /* Remove truncation for full description display */
  display: block;
  overflow: visible;
}

.solution-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive design */
@media only screen and (max-width: 768px) {
  .solution-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 16px;
  }

  .solution-card {
    padding: 20px;
  }

  .solution-name {
    font-size: 18px;
  }

  .solution-description {
    font-size: 13px;
  }
}

@media only screen and (max-width: 480px) {
  .solution-card {
    padding: 16px;
  }

  .solution-name {
    font-size: 16px;
  }
}
</style>
