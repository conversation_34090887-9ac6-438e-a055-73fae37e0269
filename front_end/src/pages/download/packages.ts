export enum PackageType {
  Enterprise = 'Enterprise',
  Docker = 'Docker',
  GSQLClient = 'GSQLClient',
  Tools = 'Tools',
  Community = 'Community',
}

export enum Edition {
  Enterprise = 'Enterprise Edition',
  Community = 'Community Edition',
}

export type Package = {
  type: string;
  version: string;
  edition: Edition;
  link: string;
  sign: string;
  checksum: string;
  endOfSupport: string;
  licenseExpDate: string;
  size: string;
};
