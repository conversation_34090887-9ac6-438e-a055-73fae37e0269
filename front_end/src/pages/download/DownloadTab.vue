<template>
  <div
    class="inner-tab"
    v-for="version in Object.keys(pkgsByVersion)"
    :key="version"
  >
    <h2
      class="inner-title"
      v-if="edition === Edition.Enterprise"
    >TigerGraph Enterprise Edition {{ version }} {{ isPreviewVersion(pkgsByVersion[version][0]) ? 'Preview' : 'LTS' }}
      <!-- <div v-if="!isPreviewVersion(pkg)">
        <span class="inner-text">Paid support until <b>{{ pkg.endOfSupport }}</b></span>
        <br>
      </div> -->
    </h2>
    <h2
      class="inner-title"
      v-else
    >TigerGraph Community Edition {{ version }} {{ isPreviewVersion(pkgsByVersion[version][0]) ? 'Preview' : '' }}</h2>

    <table class="oew-table oew-table-even download-table">
      <thead>
        <tr class="oew-table-row">
          <th class="oew-table-cell elementor-repeater-item-504a04b"><span class="oew-table-text"><span class="oew-table-text-inner">PRODUCT</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b"><span class="oew-table-text"><span class="oew-table-text-inner">OS</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b oew-table-cell elementor-repeater-item-64611ba"><span class="oew-table-text oew-table-text"><span class="oew-table-text-inner">DOWNLOAD</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b oew-table-cell elementor-repeater-item-64611ba oew-table-cell elementor-repeater-item-d6f2d9c"><span class="oew-table-text oew-table-text oew-table-text"><span class="oew-table-text-inner">SIZE</span></span></th>
          <th class="oew-table-cell elementor-repeater-item-504a04b oew-table-cell elementor-repeater-item-64611ba oew-table-cell elementor-repeater-item-d6f2d9c oew-table-cell elementor-repeater-item-d565d48"><span class="oew-table-text oew-table-text oew-table-text oew-table-text"><span class="oew-table-text-inner">Sha256Sum</span></span></th>
        </tr>
      </thead>
      <tbody>
        <tr class="oew-table-row elementor-repeater-item-ce8a89f"
          v-for="pkg in pkgsByVersion[version]"
          :key="pkg.type + pkg.version"
        >
          <td
            class="oew-table-cell elementor-repeater-item-707b39a"
            data-title="PRODUCT"
          ><span class="oew-table-text"><span class="oew-table-text-inner">{{ getProduct(pkg)}}</span></span></td>
          <td
            class="oew-table-cell elementor-repeater-item-707b39a"
            data-title="OS"
          ><span class="oew-table-text"><span class="oew-table-text-inner">LINUX X64</span></span></td>
          <td
            class="oew-table-cell elementor-repeater-item-de530ec"
            data-title="DOWNLOAD"
          >
            <form
              :id="`${pkg.type}-${pkg.version}`"
              method="get"
            >
              <a
                href="javascript:{}"
                @click="onDownload(pkg)"
              ><span class="oew-table-text-inner">{{ getProduct(pkg) }} {{ pkg.version }}<q-icon name="expand_more" /></span></a>
              <span
                style="text-transform: lowercase;"
                v-if="pkg.sign"
              >&nbsp;(<a :href="pkg.sign">sig</a>)</span>
            </form>
          </td>
          <td
            class="oew-table-cell elementor-repeater-item-ee4b408"
            data-title="SIZE"
          ><span class="oew-table-text"><span class="oew-table-text-inner">{{ pkg.size }}</span></span></td>
          <td
            class="oew-table-cell elementor-repeater-item-dbac82a"
            data-title="Sha256Sum"
          ><span class="oew-table-text"><span class="oew-table-text-inner">{{ pkg.checksum }}</span></span></td>
        </tr>
      </tbody>
    </table>
  </div>

</template>

<script lang="ts">
import { PropType, defineComponent } from 'vue';
import { Edition, Package, PackageType } from './packages';

const pkgTypeSortIndex = {
  [PackageType.Community]: 0,
  [PackageType.Enterprise]: 1,
  [PackageType.Docker]: 2,
  [PackageType.GSQLClient]: 3,
  [PackageType.Tools]: 4,
};

export default defineComponent({
  name: 'DownloadTab',
  props: {
    pkgs: {
      type: Object as PropType<Package[]>,
      required: true,
    },
    edition: {
      type: String,
    },
  },
  computed: {
    Edition() {
      return Edition;
    },
    pkgsByVersion(): Record<string, Package[]> {
      const pkgsByVersion = {} as Record<string, Package[]>;
      this.pkgs.forEach((pkg) => {
        if (!pkgsByVersion[pkg.version]) {
          pkgsByVersion[pkg.version] = [];
        }
        pkgsByVersion[pkg.version].push(pkg);
      });
      Object.keys(pkgsByVersion).forEach((version) => {
        pkgsByVersion[version] = pkgsByVersion[version].sort((a, b) => {
          return pkgTypeSortIndex[a.type] - pkgTypeSortIndex[b.type];
        });
      })
      return pkgsByVersion;
    },
  },
  methods: {
    getProduct(pkg: Package) {
      if (pkg.type === PackageType.Enterprise || pkg.type === PackageType.Community) {
        return 'TigerGraph DB Server';
      }
      if (pkg.type === PackageType.Docker) {
        return 'Docker Image';
      }
      if (pkg.type === PackageType.GSQLClient) {
        return 'GSQL Client';
      }
      if (pkg.type === PackageType.Tools) {
        return pkg.link.replace(/.*\//g, '').replace(/\.tar\.gz/g, '');
      }
      return pkg.type;
    },
    onDownload(pkg: Package) {
      this.$emit('download', pkg);
    },
    isPreviewVersion(pkg: Package) {
      return pkg.edition.toLowerCase().includes('preview');
    },
  },
});
</script>

<style lang="scss" scoped>
</style>
