[{"name": "Transaction Fraud", "graph": "Transaction_Fraud", "depends": "financial_crime/library", "description": "Credit card transaction fraud detection identifies and prevents unauthorized or deceptive transactions in real-time. It analyzes transaction data, including cardholder information, transaction details, and historical patterns, to detect anomalies and suspicious activity indicative of fraud. TigerGraph models complex relationships and patterns among entities such as cardholders, merchants, transactions, and geographic locations. This enable detection of fraudulent networks and patterns that may be difficult to uncover using traditional relational databases. Using graph algorithms enable organizations to detect and respond to fraudulent transactions quickly and efficiently, ultimately reducing financial losses and protecting consumers from fraudulent activity.\n", "categories": ["Financial", "<PERSON><PERSON>"], "algorithms": ["Page Rank"], "icon": "financial_crime/transaction_fraud/meta/icon.svg", "images": ["financial_crime/transaction_fraud/meta/images/1.png", "financial_crime/transaction_fraud/meta/images/Transaction_Fraud_Schema_Screenshot.png"], "provider": "TigerGraph", "hasInsights": true, "docLinks": ["https://tigergraph-solution-kits.s3.us-west-1.amazonaws.com/financial_crime/transaction_fraud/model/training/model_training.html", "https://tigergraph-solution-kits.s3.us-west-1.amazonaws.com/financial_crime/transaction_fraud/model/inference/model_inference.html"], "path": "financial_crime/transaction_fraud", "initQuery": "USE GRAPH Transaction_Fraud\nrun query merchant_merchant_with_weights()\nrun query card_card_with_weights()\nrun query tg_wcc_card_weight_based()\nrun query tg_wcc_merchant_weight_based()\nrun query tg_pagerank_wt_merchant()\nrun query tg_pagerank_wt_card()\n"}, {"name": "<PERSON><PERSON> Account Detection", "graph": "Mule_Account_Detection", "depends": "financial_crime/library", "description": "Mule account detection in financial crime is a critical endeavor aimed at identifying bank accounts that are used to receive and disperse money from illicit activities. These mule accounts, which may be involved either knowingly or unknowingly in these operations, present a significant challenge for financial institutions. The rapid movement of funds through an extensive and seemingly unconnected network of accounts, spread across numerous financial institutions, complicates the tracking and halting of such illicit transactions. Financial institutions are therefore tasked with detecting this activity promptly to prevent further fund transfer and ensure the return of assets to their rightful owners. TigerGraph's solutions address this challenge through a suite of sophisticated graph algorithms that enable real-time monitoring, feature engineering for machine learning, and anomaly detection to trace illegal funds effectively. Community Detection is employed to unveil clusters within the transaction network, highlighting groups of accounts that work in concert to move illicit funds. The Centrality (PageRank) algorithm identifies key accounts that act as central nodes in the distribution network, crucial for disrupting the flow of illicit money. Closeness (Shortest Path) analysis reveals the most direct routes for money laundering, aiding in the trace back to the source. Lastly, Deep Link Analysis uncovers hidden connections between accounts, providing a comprehensive understanding of the network's structure and operation. Together, these algorithms form the backbone of TigerGraph's approach to dismantling the complex networks of mule accounts, safeguarding the financial system against the movement of illicit funds.\n", "categories": ["Financial", "<PERSON><PERSON>"], "algorithms": ["Page Rank"], "icon": "financial_crime/mule_account_detection/meta/icon.svg", "images": ["financial_crime/mule_account_detection/meta/images/1.png", "financial_crime/mule_account_detection/meta/images/mule_account_detection_schema.png"], "provider": "TigerGraph", "hasInsights": true, "docLinks": [], "path": "financial_crime/mule_account_detection", "initQuery": "USE GRAPH Mule_Account_Detection\nrun query account_account_with_weights()\nrun query tg_wcc_account_with_weights()\nrun query tg_pagerank_wt_account()\nrun query tg_shortest_path_length_account()\n\n\n"}, {"name": "financial crime library", "description": "super schema for financial crime", "is_library": true, "images": [], "provider": "TigerGraph", "algorithms": [], "hasInsights": false, "docLinks": [], "path": "financial_crime/library"}, {"name": "Entity Resolution KYC", "graph": "Entity_Resolution_KYC", "depends": "financial_crime/library", "description": "Know Your Customer Entity Resolution (\"KYC ER\") is a graph database solution empowering financial institutions to resolve disparate entity customers, uncover their hidden relationships, and prevent fraud. By leveraging advanced graph analytics and algorithms financial institutions are enabled to make informed decisions, reduce risk, and drive business growth. With KYC ER financial institutions can now confidently onboard new customers, detect and prevent fraud, and optimize customer relationships.\n", "categories": ["Banking", "Finance", "Insurance"], "images": ["financial_crime/entity_resolution_kyc/meta/images/Entity_Resolution_KYC_Schema_Screenshot.png"], "provider": "TigerGraph", "algorithms": [], "hasInsights": false, "docLinks": [], "path": "financial_crime/entity_resolution_kyc"}, {"name": "Application Fraud", "graph": "Application_Fraud", "depends": "financial_crime/library", "description": "TigerGraph's solution for Application Fraud helps organizations in various industries, such as banking and finance, detect and prevent fraudulent activities during the application process. By leveraging graph analytics, machine learning, and real-time data, TigerGraph enables the identification of suspicious patterns and anomalies, reducing the risk of fraudulent applications.\n", "categories": ["Banking", "Finance", "Insurance", "E-commerce"], "icon": "financial_crime/application_fraud/meta/icon.svg", "images": ["financial_crime/application_fraud/meta/images/Application_Fraud_Schema_Screenshot.png"], "provider": "TigerGraph", "algorithms": [], "hasInsights": true, "docLinks": [], "path": "financial_crime/application_fraud"}, {"name": "Product Recommendation", "graph": "Product_Recommendation", "description": "The product recommendation solution is a powerful system that leverages advanced algorithms and machine learning techniques to provide personalized product recommendations to users. It analyzes user behavior, preferences, and historical data to understand their interests and generate relevant recommendations. By considering factors such as purchase history, browsing patterns, and demographic information, the solution delivers tailored suggestions that enhance the user experience and drive engagement. This helps businesses increase customer satisfaction, boost sales, and improve overall conversion rates.\n", "categories": ["Recommendation"], "icon": "connected_customer/product_recommendations/meta/icon.svg", "images": [], "provider": "TigerGraph", "algorithms": [], "hasInsights": false, "docLinks": [], "path": "connected_customer/product_recommendations"}, {"name": "Entity Resolution", "graph": "Entity_Resolution", "production": true, "description": "TigerGraph's Entity Resolution solution for Customer data helps organizations consolidate and unify customer records from different data sources. By leveraging graph analytics and machine learning, TigerGraph enables accurate identification and merging of customer records, improving data quality and customer insights. This solution is valuable for industries with complex customer data landscapes, such as banking, retail, and healthcare.\n", "categories": ["Manufacturing", "Retail", "Healthcare"], "icon": "connected_customer/entity_resolution/meta/icon.svg", "images": ["connected_customer/entity_resolution/meta/images/Entity_Resolution_Schema_Screenshot.png"], "provider": "TigerGraph", "algorithms": [], "hasInsights": false, "docLinks": [], "path": "connected_customer/entity_resolution"}, {"name": "Customer 360 Financial", "graph": "Customer_360_Financial", "description": "The Customer 360 Solution Kit for financial institutions is designed to provide a comprehensive view of each customer by aggregating data from various touchpoints.\n", "categories": ["Connected Customer", "Financial"], "icon": "connected_customer/customer_360/meta/icon.svg", "images": ["connected_customer/customer_360/meta/images/schema.png"], "provider": "TigerGraph", "algorithms": [], "hasInsights": false, "docLinks": [], "path": "connected_customer/customer_360"}, {"name": "Supply Chain Management", "graph": "Supply_Chain_Management", "description": "This solution kit is intended to provide a comprehensive digital twin for the domains of Manufacturing Execution, Procurement, Sales, and Inventory Management.\n", "categories": ["Supply Chain", "Inventory Management", "Digital Twin", "Sales"], "icon": "agile_operations/supply_chain_management/meta/icon.svg", "images": [], "provider": "TigerGraph", "algorithms": [], "hasInsights": false, "docLinks": [], "path": "agile_operations/supply_chain_management"}, {"name": "Network Infrastructure", "graph": "Network_Infrastructure", "description": "Enterprises have their own data centers and their own network infrastructure involving a lot of devices. Cyberattacks and other incidents can lead to issues such as data breach, corrupted files, and loss of data, resulting in billions of dollars lost each year. One aspect that can help with detecting those attacks and other incidents is to gain a better understanding of the network infrastructure of your organization. With visualizations in TigerGraph, users can gain a better visibility of the platform by seeing different components and the topology of their Network Infrastructure from data loaded from multiple sources. Different graph algorithms can be run at scale and allows for discovery of related incidents and events based on the device topology close to real-time.\n", "categories": ["Information Technology", "Cybersecurity", "Network Topology"], "icon": "agile_operations/network_infrastructure/meta/icon.svg", "images": ["agile_operations/network_infrastructure/meta/images/Network_Infrastructure_Schema_Image.png"], "provider": "TigerGraph", "algorithms": [], "hasInsights": true, "docLinks": [], "path": "agile_operations/network_infrastructure"}]