package email

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"log"

	"github.com/matcornic/hermes/v2"
	"github.com/tigergraph/download_experience/db"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ses"
)

type Service struct {
	Sender     string
	Attempts   int
	WaitPeriod time.Duration
	SES        *ses.SES
}

func NewService(sender string, attempts int, waitPeriod time.Duration) (*Service, error) {
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("us-west-1"),
	})
	if err != nil {
		return nil, err
	}

	return &Service{
		Sender:     sender,
		Attempts:   attempts,
		WaitPeriod: waitPeriod,
		SES:        ses.New(sess),
	}, nil
}

func (s *Service) SendEmail(recipient, subject, emailHTML, emailText string) error {
	return s.Send(s.Sender, recipient, subject, emailHTML, emailText, s.Attempts, s.WaitPeriod)
}

// Send sends the email to the recipient using AWS SES.
// attempts and waitPeriod parameters allow to configure
// behavior in the case of SES failure.
func (s *Service) Send(sender, recipient, subject, emailHTML, emailText string, attempts int, waitPeriod time.Duration) error {
	input := &ses.SendEmailInput{
		Destination: &ses.Destination{
			CcAddresses: []*string{},
			ToAddresses: []*string{
				aws.String(recipient),
			},
		},
		Message: &ses.Message{
			Body: &ses.Body{
				Html: &ses.Content{
					Charset: aws.String("UTF-8"),
					Data:    aws.String(emailHTML),
				},
				Text: &ses.Content{
					Charset: aws.String("UTF-8"),
					Data:    aws.String(emailText),
				},
			},
			Subject: &ses.Content{
				Charset: aws.String("UTF-8"),
				Data:    aws.String(subject),
			},
		},
		Source: aws.String(sender),
		// Uncomment to use a configuration set
		ConfigurationSetName: aws.String("EmailTrackingConfigSet"),
	}

	// Attempt to send the email.
	result, err := s.SES.SendEmail(input)

	for err != nil && attempts > 1 {
		if err != nil {
			log.Printf("Error sending email: %v", err)
		}
		attempts--
		time.Sleep(waitPeriod)
		result, err = s.SES.SendEmail(input)
	}

	// Display error messages if they occur.
	err = s.handleError(err)
	if err != nil {
		return err
	}

	log.Printf("Email with subject \"%v\" sent to %v.", subject, recipient)
	log.Println(result)

	return nil
}

func (s *Service) handleError(err error) error {
	// Display error messages if they occur.
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			switch aerr.Code() {
			case ses.ErrCodeMessageRejected:
				log.Println(ses.ErrCodeMessageRejected, aerr.Error())
			case ses.ErrCodeMailFromDomainNotVerifiedException:
				log.Println(ses.ErrCodeMailFromDomainNotVerifiedException, aerr.Error())
			case ses.ErrCodeConfigurationSetDoesNotExistException:
				log.Println(ses.ErrCodeConfigurationSetDoesNotExistException, aerr.Error())
			default:
				log.Println(aerr.Error())
			}
		} else {
			// Print the error, cast err to awserr.Error to get the Code and Message from an error.
			log.Println(err.Error())
		}

		return err
	}

	return nil
}

// Configure hermes by setting a theme and your product info
var hermesTempl = hermes.Hermes{
	// Optional Theme
	// Theme: new(Default)
	Product: hermes.Product{
		// Appears in header & footer of e-mails
		Name: "TigerGraph Inc.",
		Link: "https://www.tigergraph.com/",
		// Optional product logo
		Logo:      "https://tigergraph-cloud-portal-static.s3-us-west-1.amazonaws.com/tigergraph-full-color-logo.png",
		Copyright: fmt.Sprintf("Copyright © %v TigerGraph", time.Now().Year()),
	},
}

func (svc *Service) SendPackageEmail(packageInfo db.PackageInfo, name, to, packageURL, licenseURL string) error {
	version := packageInfo.Version
	if packageInfo.Type == db.Tools {
		version = getToolsVersion(packageInfo)
	}
	mapTypeToName := map[db.PackageType]string{
		db.Enterprise: "DB Server",
		db.Docker:     "Docker Image",
		db.Tools:      "Tools",
		db.GSQLClient: "GSQL Client",
	}
	packageName := fmt.Sprintf("TigerGraph %s %s", mapTypeToName[packageInfo.Type], version)
	if packageInfo.Edition == db.CommunityEd {
		packageName = fmt.Sprintf("TigerGraph Community Edition %s %s", mapTypeToName[packageInfo.Type], version)
	}
	email := hermes.Email{
		Body: hermes.Body{
			Greeting: "Dear",
			Name:     name,
			Intros: []string{
				"Thank you for expressing interest in TigerGraph, the only scalable platform for analytics and machine learning on connected data.",
			},
		},
	}
	email.Body.Actions = []hermes.Action{
		{
			Instructions: fmt.Sprintf("To download %s, please click the button below.", packageName),
			Button: hermes.Button{
				Color:     "#FB9A44",
				TextColor: "#222222",
				Text:      fmt.Sprintf("Download %s", packageName),
				Link:      packageURL,
			},
		},
	}
	if (packageInfo.Type == db.Enterprise || packageInfo.Type == db.Docker) && packageInfo.Edition != db.CommunityEd {
		email.Body.Actions = append(email.Body.Actions, hermes.Action{
			Instructions: "To download your license key, please click the button below.",
			Button: hermes.Button{
				Color:     "#FB9A44",
				TextColor: "#222222",
				Text:      "Download License Key",
				Link:      licenseURL,
			},
		})
	}

	email.Body.Outros = []string{
		"Once you have downloaded TigerGraph, you will need to activate it using the license key.",
	}
	if packageInfo.Edition != db.CommunityEd {
		email.Body.Outros = append(email.Body.Outros, "If you encounter any issues during the installation process, please contact <NAME_EMAIL>.")
	} else {
		email.Body.Outros = append(email.Body.Outros, "If you encounter any issues, please visit our community support page at https://www.tigergraph.com/community/.")
	}
	email.Body.Outros = append(email.Body.Outros, "For the latest updates and release notes, please check our Release Notes at https://docs.tigergraph.com/tigergraph-server/current/release-notes/.")

	html, err := hermesTempl.GenerateHTML(email)
	if err != nil {
		return fmt.Errorf("failed to generate email html: %w", err)
	}

	return svc.SendEmail(to, fmt.Sprintf("Thanks for Downloading %s", packageName), html, "")
}

func (svc *Service) SendCommunityPackageEmail(packageInfo db.PackageInfo, name, to, packageURL string) error {
	version := packageInfo.Version
	if packageInfo.Type == db.Tools {
		version = getToolsVersion(packageInfo)
	}
	mapTypeToName := map[db.PackageType]string{
		db.Enterprise: "DB Server",
		db.Docker:     "Docker Image",
		db.Tools:      "Tools",
		db.GSQLClient: "GSQL Client",
	}
	packageName := fmt.Sprintf("TigerGraph %s %s", mapTypeToName[packageInfo.Type], version)
	if packageInfo.Edition == db.CommunityEd {
		packageName = fmt.Sprintf("TigerGraph Community Edition %s %s", mapTypeToName[packageInfo.Type], version)
	}
	content := fmt.Sprintf(`
# Welcome to the Future of AI-Powered Graph Analytics
Thank you for signing up to download TigerGraph Community Edition, the most powerful free graph and vector database for AI-driven applications. Your journey into graph-powered AI, real-time analytics, and knowledge graph innovation starts now.
## Download Your Copy Now
<div style="text-align: center; margin-bottom: 30px;"><a href="%s" target="_blank" style="display: inline-block; background-color: #fb9a44; color: #222; text-decoration: none; border-radius: 3px; font-size: 15px; min-width: 300px; text-align: center; line-height: 45px;padding: 0 16px;">Download %s</a></div>
`, packageURL, packageName)

	if packageInfo.Type == db.Docker {
		content += `
# Set Up Environment
If you have your own machine (including Windows and Mac laptops), the easiest way to run TigerGraph is to install it as a Docker image. Follow the [Docker setup instructions](https://github.com/tigergraph/ecosys/blob/master/demos/guru_scripts/docker/README.md) to set up the environment on your machine. After you installed TigerGraph, you can use gadmin tool to start/stop services under Bash shell.

`
	}

	content += `
# Get the Most Out of Your TigerGraph Experience
## Documentation & Quick Start Guide
Learn how to install and configure TigerGraph using Docker.
- [Read the Docs](https://docs.tigergraph.com/tigergraph-server/4.1/getting-started/docker)

## Release Notes
For a complete list of new features, enhancements, and fixes in TigerGraph, refer to the latest release notes.  [View Release Notes](https://docs.tigergraph.com/tigergraph-server/4.1/release-notes/)
## Tutorials & Learning Resources
- Learn how to write powerful queries in TigerGraph. [Graph Query Language Tutorials](https://github.com/tigergraph/ecosys/blob/master/tutorials/GSQL.md)
- Learn how to write OpenCypher queries in TigerGraph. [OpenCypher Tutorial](https://github.com/tigergraph/ecosys/blob/master/tutorials/Cypher.md)
- Learn how to integrate vector search with graph queries for AI-powered applications. [Vector Search Tutorial](https://github.com/tigergraph/ecosys/blob/master/demos/guru_scripts/docker/tutorial/4.x/VectorSearch.md)

## Demos & Walkthroughs
- Community Edition Demo – Get started with graph analytics and AI-driven applications. [Watch Community Edition Demo](https://www.youtube.com/watch?v=kKj5-EGQGa4)
- Vector Search Demo – Learn how TigerGraph’s hybrid vector and graph search enhances AI-powered search. [Watch Vector Search Demo](https://www.youtube.com/watch?v=JSE41EIBpNk)

# Join the TigerGraph Community
Connect with other developers, ask questions, and share insights.
[Join the Community Forum](https://www.tigergraph.com/community/)
# Compare TigerGraph Editions
Not sure if Community Edition is the right fit? Explore the differences between TigerGraph Enterprise and Community Edition to see which one best meets your needs. [Compare Editions](https://www.tigergraph.com/comparison-of-tigergraph-editions/)
# Ready to Scale Up? Upgrade to TigerGraph Enterprise
TigerGraph Enterprise offers multi-node clustering, high availability, CRR, advanced security features, and enterprise-grade support for mission-critical applications.
If you are interested in upgrading to Enterprise Edition, reach out to <NAME_EMAIL> for more information.
If you have any questions, we’re here to help. Reach out to the community forum or check out our documentation.
`

	email := hermes.Email{
		Body: hermes.Body{
			Greeting:     "Dear",
			Name:         name,
			FreeMarkdown: hermes.Markdown(content),
		},
	}

	html, err := hermesTempl.GenerateHTML(email)
	if err != nil {
		return fmt.Errorf("failed to generate email html: %w", err)
	}

	return svc.SendEmail(to, "Your TigerGraph Community Edition Download is Ready!", html, "")
}

func (svc *Service) SendLicenseEmail(name, to, licenseURL string) error {
	email := hermes.Email{
		Body: hermes.Body{
			Greeting: "Dear",
			Name:     name,
			Intros: []string{
				"Thank you for expressing interest in TigerGraph, the only scalable platform for analytics and machine learning on connected data.",
			},
		},
	}
	email.Body.Actions = []hermes.Action{
		{
			Instructions: " Please click the button below to download your license key. Please note that the license will be valid for a period of 1 month from today. After this period, you will need to renew your license by contacting our sales team to continue using TigerGraph.",
			Button: hermes.Button{
				Color:     "#FB9A44",
				TextColor: "#222222",
				Text:      "Download License Key",
				Link:      licenseURL,
			},
		},
	}
	email.Body.Outros = []string{
		"If you encounter any issues during the installation process, please contact <NAME_EMAIL>.",
		"For the latest updates and release notes, please check our Release Notes at https://docs.tigergraph.com/tigergraph-server/current/release-notes/.",
	}
	html, err := hermesTempl.GenerateHTML(email)
	if err != nil {
		return fmt.Errorf("failed to generate license email html: %w", err)
	}
	return svc.SendEmail(to, "Thanks for Requesting TigerGraph Free Dev License", html, "")
}

func (svc *Service) SendSolutionEmail(packageInfo db.PackageInfo, name, to, solutionWithDataURL, solutionWithoutDataURL string) error {
	// Extract solution name from file path (e.g., "financial_crime/transaction_fraud" -> "Transaction Fraud")
	solutionName := getSolutionName(packageInfo.FileName)

	content := fmt.Sprintf(`
Thank you for your interest in the **%s** solution kit from TigerGraph!

We're excited to provide you with this comprehensive solution that demonstrates the power of graph analytics. You have two download options:

## Download Options

**Option 1: Solution with Sample Data**
- Includes the complete solution with sample data for immediate testing
- Perfect for evaluation and getting started quickly
- [Download Solution with Data](%s)

**Option 2: Solution without Data**
- Contains the solution schema and queries only
- Ideal for production deployment with your own data
- [Download Solution without Data](%s)

## Getting Started

- Check out our [documentation](https://docs.tigergraph.com/gui/4.2/graphstudio/export-and-import-solution#_import) for detailed guidance on importing the solution kit

## Need Help?

- Visit our [Community Forum](https://dev.tigergraph.com/) for questions and discussions
- Check out our [Documentation](https://docs.tigergraph.com/) for comprehensive guides
- Contact our support <NAME_EMAIL> for technical assistance

We're excited to see what you'll build with TigerGraph!

Best regards,
The TigerGraph Team
`, solutionName, solutionWithDataURL, solutionWithoutDataURL)

	email := hermes.Email{
		Body: hermes.Body{
			Greeting:     "Dear",
			Name:         name,
			FreeMarkdown: hermes.Markdown(content),
		},
	}

	html, err := hermesTempl.GenerateHTML(email)
	if err != nil {
		return fmt.Errorf("failed to generate solution email html: %w", err)
	}

	return svc.SendEmail(to, fmt.Sprintf("Your %s Solution Kit Download is Ready!", solutionName), html, "")
}

func getSolutionName(filePath string) string {
	// Convert file path like "financial_crime/transaction_fraud" to "Transaction Fraud"
	parts := strings.Split(filePath, "/")
	if len(parts) < 2 {
		return "TigerGraph Solution"
	}

	// Take the last part (solution name) and convert to title case
	solutionPart := parts[len(parts)-1]
	words := strings.Split(solutionPart, "_")
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}
	return strings.Join(words, " ")
}

func getToolsVersion(packageInfo db.PackageInfo) string {
	// file name is like tools-all-signed-1.0.3659.0.tar.gz
	versionPattern := `tools-all-signed-(\d+\.\d+\.\d+\.\d+)`
	regex, _ := regexp.Compile(versionPattern)
	matches := regex.FindStringSubmatch(packageInfo.FileName)
	if len(matches) < 2 {
		return packageInfo.Version + " Latest"
	}

	return fmt.Sprintf("%s %s", packageInfo.Version, matches[1])
}
