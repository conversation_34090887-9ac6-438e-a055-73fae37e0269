package email

import (
	"gopkg.in/gomail.v2"
)

// SendEmailReq is the request body for sending an email
type SendEmailReq struct {
	Host        string
	Port        int
	Username    string
	Password    string
	From        string
	To          []string
	Subject     string
	ContentType string
	Body        string
}

// SendEmail sends an email with given smtp server info and email data
func SendEmail(req *SendEmailReq) error {
	d := gomail.NewDialer(req.Host, req.Port, req.Username, req.Password)
	m := gomail.NewMessage()
	m.SetHeader("From", req.From)
	m.SetHeader("To", req.To...)
	m.SetHeader("Subject", req.Subject)
	m.SetBody(req.ContentType, req.Body)
	return d.DialAndSend(m)
}
