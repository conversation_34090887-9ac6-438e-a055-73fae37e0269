package db

import (
	"context"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

type UserInfo struct {
	Email              string `json:"email" dynamodbav:"email"`
	FirstName          string `json:"first_name" dynamodbav:"first_name"`
	LastName           string `json:"last_name" dynamodbav:"last_name"`
	CompanyName        string `json:"company_name" dynamodbav:"company_name"`
	Phone              string `json:"phone" dynamodbav:"phone"`
	JobCategory        string `json:"job_category" dynamodbav:"job_category"`
	Country            string `json:"country" dynamodbav:"country"`
	LinkedinProfileURL string `json:"linkedin_profile_url" dynamodbav:"linkedin_profile_url"`
}

type PackageType string

const (
	Developer   PackageType = "Developer"
	Enterprise  PackageType = "Enterprise"
	Docker      PackageType = "Docker"
	VMImage     PackageType = "VMImage"
	GSQLClient  PackageType = "GSQLClient"
	MLWorkbench PackageType = "MLWorkbench"
	Tools       PackageType = "Tools"
	License     PackageType = "License"
	Community   PackageType = "Community"
	Solution    PackageType = "solution"
)

type PackageEdition string

const (
	CommunityEd  PackageEdition = "Community Edition"
	EnterpriseEd PackageEdition = "Enterprise Edition"
)

type PackageInfo struct {
	Type     PackageType    `json:"type" dynamodbav:"type"`
	Version  string         `json:"version" dynamodbav:"version"`
	FileName string         `json:"file_name" dynamodbav:"file_name"`
	Edition  PackageEdition `json:"edition" dynamodbav:"edition"`
	SolutionName string     `json:"solution_name" dynamodbav:"solution_name"`
}
type DownloadRequest struct {
	UserInfo    UserInfo    `json:"user_info"`
	PackageInfo PackageInfo `json:"package_info"`
}
type DownloadRequestItem struct {
	RequestId      string      `json:"request_id" dynamodbav:"request_id"`
	RequestTime    int64       `json:"request_time" dynamodbav:"request_time"`
	ExpirationTime int64       `json:"expiration_time" dynamodbav:"expiration_time"`
	Email          string      `json:"email" dynamodbav:"email"`
	UserInfo       UserInfo    `json:"user_info" dynamodbav:"user_info"`
	PackageInfo    PackageInfo `json:"package_info" dynamodbav:"package_info"`
	Completed      bool        `json:"completed" dynamodbav:"completed"`
}

type EmailEvent struct {
	MessageID string                 `dynamodbav:"messageId"`
	Timestamp int64                  `dynamodbav:"timestamp"`
	DateTime  string                 `dynamodbav:"datetime"`
	EventType string                 `dynamodbav:"eventType"`
	Recipient string                 `dynamodbav:"recipient"`
	Details   map[string]interface{} `dynamodbav:"details"`
}

var tablename = os.Getenv("DB_TABLE_NAME")

type DBService struct {
	client *dynamodb.Client
}

func New(cfg aws.Config) *DBService {
	return &DBService{
		client: dynamodb.NewFromConfig(cfg),
	}
}

func (db *DBService) GetDownloadRequest(ctx context.Context, requestId string) (item DownloadRequestItem, err error) {
	response, err := db.client.GetItem(ctx, &dynamodb.GetItemInput{
		TableName: aws.String(tablename),
		Key: map[string]types.AttributeValue{
			"request_id": &types.AttributeValueMemberS{
				Value: requestId,
			},
		},
	})
	if err != nil {
		log.Printf("Error getting item from dynamodb: %v", err)
		return
	}
	err = attributevalue.UnmarshalMap(response.Item, &item)
	if err != nil {
		log.Printf("Error unmarshalling item from dynamodb: %v", err)
		return
	}
	return item, nil
}

func (db *DBService) PutDownloadRequest(ctx context.Context, item DownloadRequestItem) error {
	av, err := attributevalue.MarshalMap(item)
	if err != nil {
		log.Printf("Error marshalling item: %v", err)
		return err
	}
	_, err = db.client.PutItem(ctx, &dynamodb.PutItemInput{
		Item:      av,
		TableName: &tablename,
	})
	if err != nil {
		log.Printf("Error putting item to dynamodb: %v", err)
	}
	return err
}

func (db *DBService) PutEmailEvent(ctx context.Context, item EmailEvent) error {
	const tablename = "EmailEvents"
	av, err := attributevalue.MarshalMap(item)
	if err != nil {
		log.Printf("Error marshalling item: %v", err)
		return err
	}
	_, err = db.client.PutItem(ctx, &dynamodb.PutItemInput{
		Item:      av,
		TableName: aws.String(tablename),
	})
	if err != nil {
		log.Printf("Error putting item to dynamodb: %v", err)
	}
	return err
}

func (db *DBService) UpdateDownloadRequest(ctx context.Context, requestId string) error {
	_, err := db.client.UpdateItem(ctx, &dynamodb.UpdateItemInput{
		TableName: aws.String(tablename),
		Key: map[string]types.AttributeValue{
			"request_id": &types.AttributeValueMemberS{
				Value: requestId,
			},
		},
		UpdateExpression: aws.String("SET completed = :c"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":c": &types.AttributeValueMemberBOOL{
				Value: true,
			},
		},
	})
	if err != nil {
		log.Printf("Error updating item in dynamodb: %v", err)
		return err
	}
	return nil
}

type IndexItem struct {
	Email       string      `json:"email" dynamodbav:"email"`
	RequestTime int64       `json:"request_time" dynamodbav:"request_time"`
	PackageInfo PackageInfo `json:"package_info" dynamodbav:"package_info"`
}

func (db *DBService) HasDownloadRequestsWithin5Minutes(ctx context.Context, email string, packageInfo PackageInfo) (bool, error) {
	response, err := db.client.Query(ctx, &dynamodb.QueryInput{
		TableName:              aws.String(tablename),
		IndexName:              aws.String("email-request_time-index"),
		KeyConditionExpression: aws.String("email = :e AND request_time > :t"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":e": &types.AttributeValueMemberS{
				Value: email,
			},
			":t": &types.AttributeValueMemberN{
				Value: strconv.FormatInt(time.Now().Add(-5*time.Minute).Unix(), 10),
			},
		},
	})
	if err != nil {
		log.Printf("Error calling Query: %v", err)
		return false, err
	}

	for _, item := range response.Items {
		var downloadRequestItem IndexItem
		err = attributevalue.UnmarshalMap(item, &downloadRequestItem)
		if err != nil {
			log.Printf("Error unmarshalling item from dynamodb: %v", err)
			return false, err
		}
		if downloadRequestItem.PackageInfo.Type == packageInfo.Type &&
			downloadRequestItem.PackageInfo.Version == packageInfo.Version &&
			downloadRequestItem.PackageInfo.FileName == packageInfo.FileName &&
			downloadRequestItem.PackageInfo.Edition == packageInfo.Edition {
			return true, nil
		}
	}
	return false, nil
}
