ARTIFACTS_DIR := bin

build:
	GOOS=linux GOARCH=amd64 go build -tags lambda.norpc -o bootstrap cmd/downloadRequest/main.go
	zip $(ARTIFACTS_DIR)/downloadRequest.zip bootstrap
	GOOS=linux GOARCH=amd64 go build -tags lambda.norpc -o bootstrap cmd/downloadPkgs/main.go
	zip $(ARTIFACTS_DIR)/downloadPkgs.zip bootstrap
	GOOS=linux GOARCH=amd64 go build -tags lambda.norpc -o bootstrap cmd/releaseInfo/main.go
	zip $(ARTIFACTS_DIR)/releaseInfo.zip bootstrap
	GOOS=linux GOARCH=amd64 go build -tags lambda.norpc -o bootstrap cmd/sendWeeklyReport/main.go
	zip $(ARTIFACTS_DIR)/sendWeeklyReport.zip bootstrap
	GOOS=linux GOARCH=amd64 go build -tags lambda.norpc -o bootstrap cmd/saveEmailEvents/main.go
	zip $(ARTIFACTS_DIR)/saveEmailEvents.zip bootstrap
