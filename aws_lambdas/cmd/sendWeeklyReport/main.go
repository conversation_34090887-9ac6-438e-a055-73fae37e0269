package main

import (
	"context"
	"encoding/csv"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/slack-go/slack"
	"github.com/tigergraph/download_experience/db"
)

func handler(ctx context.Context, event events.CloudWatchEvent) error {
	log.Println("Received CloudWatch event:", event)
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion("us-west-1"))
	if err != nil {
		log.Println("Error loading AWS config:", err)
		return err
	}

	items, err := getOneWeekDownloadItems(event, ctx, cfg)
	if err != nil {
		return err
	}

	csvStr, _, err := buildCSVStr(items)
	if err != nil {
		return err
	}
	log.Println("The result is:", csvStr)
	return sendSlackMessage(csvStr)
}

func getOneWeekDownloadItems(event events.CloudWatchEvent, ctx context.Context, cfg aws.Config) (items []map[string]types.AttributeValue, err error) {
	// Create a DynamoDB client
	client := dynamodb.NewFromConfig(cfg)

	// Create the filter expression
	oneWeekAgo := time.Now().AddDate(0, 0, -7).Unix()
	filterExpression := "completed = :completed AND request_time >= :oneWeekAgo"
	expressionAttributeValues := map[string]types.AttributeValue{
		":completed": &types.AttributeValueMemberBOOL{
			Value: true,
		},
		":oneWeekAgo": &types.AttributeValueMemberN{
			Value: strconv.FormatInt(oneWeekAgo, 10),
		},
	}

	// Create the scan input parameters
	input := &dynamodb.ScanInput{
		TableName:                 aws.String("DownloadRequests"),
		FilterExpression:          aws.String(filterExpression),
		ExpressionAttributeValues: expressionAttributeValues,
	}

	// Perform the scan operation
	result, err := client.Scan(ctx, input)
	if err != nil {
		log.Println("Error scanning DynamoDB table:", err)
	}
	items = result.Items
	return
}

func buildCSVStr(items []map[string]types.AttributeValue) (csvStr string, companys []string, err error) {
	csvBuilder := &strings.Builder{}
	csvWriter := csv.NewWriter(csvBuilder)

	// Write the header row
	header := []string{"Edition", "Email", "User", "Time", "Company", "Phone", "Job Category", "Country", "Linkedin Profile Url", "DL Type", "DL Version"}
	csvWriter.Write(header)

	if err != nil {
		log.Println("Error writing CSV header:", err)
		return
	}

	// Write the filtered items to the CSV file
	for _, item := range items {
		downloadRequest := db.DownloadRequestItem{}
		err = attributevalue.UnmarshalMap(item, &downloadRequest)
		if err != nil {
			log.Println("Error unmarshaling DynamoDB item:", err)
			continue
		}

		cmpName := strings.ToLower(downloadRequest.UserInfo.CompanyName)
		if cmpName == "test" || cmpName == "tigergraph" || strings.Contains(downloadRequest.Email, "tigergraph") {
			continue
		}

		// Extract the required fields from the DownloadRequestItem
		tz, _ := time.LoadLocation("Asia/Shanghai")
		row := []string{
			string(downloadRequest.PackageInfo.Edition),
			downloadRequest.Email,
			downloadRequest.UserInfo.FirstName + " " + downloadRequest.UserInfo.LastName,
			time.Unix(downloadRequest.RequestTime, 0).In(tz).Format("2006-01-02T15:04:05"),
			downloadRequest.UserInfo.CompanyName,
			downloadRequest.UserInfo.Phone,
			downloadRequest.UserInfo.JobCategory,
			downloadRequest.UserInfo.Country,
			downloadRequest.UserInfo.LinkedinProfileURL,
			string(downloadRequest.PackageInfo.Type),
			downloadRequest.PackageInfo.Version,
		}
		if row[0] == "" {
			row[0] = string(db.EnterpriseEd)
		}
		csvWriter.Write(row)
		companys = append(companys, downloadRequest.UserInfo.CompanyName)
	}
	csvWriter.Flush()
	csvStr = csvBuilder.String()

	return
}

func sendSlackMessage(csvStr string) error {
	token := "********************************************************"
	client := slack.New(token)
	_, err := client.UploadFileV2(slack.UploadFileV2Parameters{
		Content: csvStr,
		Channel: "C06BER1HNTF",
		Filename: "download-weekly-report.csv",
		Title: "Download Weekly Report",
		FileSize: len(csvStr),
	})
	return err
}

func main() {
	lambda.Start(handler)
}
