package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type PackageType string

const (
	Developer   PackageType = "Developer"
	Enterprise  PackageType = "Enterprise"
	Docker      PackageType = "Docker"
	VMImage     PackageType = "VMImage"
	GSQLClient  PackageType = "GSQLClient"
	MLWorkbench PackageType = "MLWorkbench"
	Tools       PackageType = "Tools"
)

type Package struct {
	Type           PackageType `json:"type"`
	Version        string      `json:"version"`
	Edition        string      `json:"edition"`
	Link           string      `json:"link"`
	Sign           string      `json:"sign"`
	Checksum       string      `json:"checksum"`
	EndOfSupport   string      `json:"endOfSupport"`
	LicenseExpDate string      `json:"licenseExpDate"`
	Size           string      `json:"size"`
}

func ParseDownloadConfig(ctx context.Context) ([]Package, error) {
	packages := make([]Package, 0)
	bucket := os.Getenv("S3_REALEASE_BUCKET")
	accessKeyId := "********************"
	secretAccessKey := "I8IVWe0GZ+3SO8gSOrGFa2y6tVvwBKhQlAg+/PPS"
	cfg, _ := config.LoadDefaultConfig(ctx, config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKeyId, secretAccessKey, "")),
		config.WithRegion("us-west-1"))
	key := os.Getenv("S3_RELEASE_FILE")
	client := s3.NewFromConfig(cfg)
	req := &s3.GetObjectInput{
		Bucket: &bucket,
		Key:    &key,
	}
	resp, err := client.GetObject(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("Failed to get object: %s", err)
	}
	defer resp.Body.Close()
	file := resp.Body

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()

		if strings.HasPrefix(line, "#") {
			continue
		}

		items := strings.Split(line, "|")
		pkg := Package{
			Type:           PackageType(items[0]),
			Version:        items[1],
			Edition:        items[2],
			Link:           items[3],
			Checksum:       items[4],
			Size: items[5],
			EndOfSupport:   items[6],
			LicenseExpDate: items[7],
		}
		if pkg.Type == Enterprise {
			pkg.Sign = pkg.Link + ".sig"
		}
		packages = append(packages, pkg)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("Error reading file: %s", err)
	}

	return packages, nil
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	pkgs, err := ParseDownloadConfig(ctx)
	if err != nil {
		log.Printf("Error parsing download config: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}
	body, err := json.Marshal(pkgs)	
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}
	res := events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: string(body),
	}
	return res, nil
}

func setCORS(response *events.APIGatewayProxyResponse) {
	response.Headers = make(map[string]string)
	response.Headers["Access-Control-Allow-Origin"] = "*"
	response.Headers["Access-Control-Allow-Methods"] = "POST, OPTIONS, PUT, DELETE"
	response.Headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
	response.Headers["Access-Control-Allow-Credentials"] = "true"
}

func main() {
	lambda.Start(func(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
		res, err := handler(ctx, request)
		setCORS(&res)
		return res, err
	})
}
