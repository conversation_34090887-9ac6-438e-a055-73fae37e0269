package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/tigergraph/download_experience/db"
)

type SESEvent struct {
	EventType string `json:"eventType"`
	Mail      struct {
		MessageId   string   `json:"messageId"`
		Destination []string `json:"destination"`
	} `json:"mail"`
	Timestamp     string                 `json:"timestamp"`
	Click         map[string]interface{} `json:"click,omitempty"`
	Open          map[string]interface{} `json:"open,omitempty"`
	Bounce        map[string]interface{} `json:"bounce,omitempty"`
	Delivery      map[string]interface{} `json:"delivery,omitempty"`
	Reject        map[string]interface{} `json:"reject,omitempty"`
	DeliveryDelay map[string]interface{} `json:"deliveryDelay,omitempty"`
}

func handler(ctx context.Context, snsEvent events.SNSEvent) {
	cfg, _ := config.LoadDefaultConfig(ctx)
	dbService := db.New(cfg)

	for _, record := range snsEvent.Records {
		var sesEvent SESEvent
		err := json.Unmarshal([]byte(record.SNS.Message), &sesEvent)
		if err != nil {
			log.Printf("error unmarshalling SNS message: %v", err)
			continue
		}

		// Ensure there's at least one recipient
		var recipient string
		if len(sesEvent.Mail.Destination) > 0 {
			recipient = sesEvent.Mail.Destination[0]
		} else {
			recipient = "unknown"
		}

		// Parse the timestamp
		t, err := time.Parse(time.RFC3339Nano, sesEvent.Timestamp)
		if err != nil {
			t = time.Now()
		}

		item := db.EmailEvent{
			MessageID: sesEvent.Mail.MessageId,
			Timestamp: t.UnixMilli(),
			DateTime:  t.Format(time.RFC3339),
			EventType: sesEvent.EventType,
			Recipient: recipient,
			Details:   make(map[string]interface{}),
		}

		if sesEvent.Click != nil {
			item.Details = sesEvent.Click
		}
		if sesEvent.Open != nil {
			item.Details = sesEvent.Open
		}
		if sesEvent.Bounce != nil {
			item.Details = sesEvent.Bounce
		}
		if sesEvent.Delivery != nil {
			item.Details = sesEvent.Delivery
		}
		if sesEvent.Reject != nil {
			item.Details = sesEvent.Reject
		}
		if sesEvent.DeliveryDelay != nil {
			item.Details = sesEvent.DeliveryDelay
		}

		err = dbService.PutEmailEvent(ctx, item)
		if err != nil {
			log.Printf("error saving email event to DynamoDB: %v", err)
			continue
		}

		fmt.Printf("Successfully processed %s event for message %s\n",
			sesEvent.EventType, sesEvent.Mail.MessageId)
	}
}

func main() {
	lambda.Start(handler)
}
