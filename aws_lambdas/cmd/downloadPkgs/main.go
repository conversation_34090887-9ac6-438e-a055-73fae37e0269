package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/tigergraph/download_experience/db"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("request resource", request.Resource)
	if request.Resource == "/download/license" || request.Resource == "/download/package" {
		return downloadHandler(ctx, request)
	}
	return events.APIGatewayProxyResponse{
		StatusCode: 404,
		Body:       "Not Found",
	}, nil
}

func downloadHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	cfg, _ := config.LoadDefaultConfig(ctx)
	dbService := db.New(cfg)
	requestId := request.QueryStringParameters["id"]
	if requestId == "" {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       "Bad Request",
		}, nil
	}
	decoded, err := base64.StdEncoding.DecodeString(requestId)
	if err != nil {
		log.Printf("Error decoding request id: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       "Bad Request",
		}, nil
	}
	requestId = string(decoded)

	item, err := dbService.GetDownloadRequest(ctx, requestId)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: 404,
			Body:       "Not Found",
		}, nil
	}

	if item.ExpirationTime < time.Now().Unix() {
		return events.APIGatewayProxyResponse{
			StatusCode: 410,
			Body:       "The link is expired.",
		}, nil
	}

	// update completed to true in dynamodb
	_ = dbService.UpdateDownloadRequest(ctx, requestId)

	if request.Resource == "/download/license" {
		return downloadLicense()
	}
	return downloadPackage(ctx, item.PackageInfo)
}

func downloadPackage(ctx context.Context, packageInfo db.PackageInfo) (events.APIGatewayProxyResponse, error) {
	packageObjectName := getS3Path(packageInfo)
	bucket := os.Getenv("S3_REALEASE_BUCKET")
	accessKeyId := "********************"
	secretAccessKey := "I8IVWe0GZ+3SO8gSOrGFa2y6tVvwBKhQlAg+/PPS"
	cfg, _ := config.LoadDefaultConfig(ctx, config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKeyId, secretAccessKey, "")),
		config.WithRegion("us-west-1"))
	s3Client := s3.NewFromConfig(cfg)
	presignClient := s3.NewPresignClient(s3Client)

	req, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(packageObjectName),
	}, func(opts *s3.PresignOptions) {
		opts.Expires = 5 * time.Minute
	})
	if err != nil {
		log.Printf("Error presigning url: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}
	// redirect user to package url
	return events.APIGatewayProxyResponse{
		StatusCode: 302,
		Headers: map[string]string{
			"Location": req.URL,
		},
	}, nil
}

func getS3Path(packageInfo db.PackageInfo) string {
	path := ""
	directory := "enterprise-edition/"
	if packageInfo.Type != db.Tools && packageInfo.Edition == db.CommunityEd {
		directory = "community-edition/"
	}
	switch packageInfo.Type {
	case db.Enterprise:
		if packageInfo.Edition == db.CommunityEd{
			path = fmt.Sprintf("tigergraph-%s-community.tar.gz", packageInfo.Version)
		} else {
			path = fmt.Sprintf("tigergraph-%s-offline.tar.gz", packageInfo.Version)
		}
	case db.Community:
		if packageInfo.Edition == db.CommunityEd{
			path = fmt.Sprintf("tigergraph-%s-community.tar.gz", packageInfo.Version)
		} else {
			path = fmt.Sprintf("tigergraph-%s-offline.tar.gz", packageInfo.Version)
		}
	case db.Docker:
		if packageInfo.Edition == db.CommunityEd {
			path = fmt.Sprintf("tigergraph-%s-community-docker-image.tar.gz", packageInfo.Version)
		} else {
			path = fmt.Sprintf("tigergraph-%s-offline-docker-image.tar.gz", packageInfo.Version)
		}
	case db.GSQLClient:
			path = fmt.Sprintf("gsql_client/tigergraph-%s-gsql_client.jar", packageInfo.Version)
	case db.Tools:
		path = fmt.Sprintf("app-tools/%s/%s", packageInfo.Version, packageInfo.FileName)
	}
	return directory + path
}

func downloadLicense() (events.APIGatewayProxyResponse, error) {
	license, length, err := getLicense()
	if err != nil {
		log.Printf("Error getting license: %v\n", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}

	// return license as a file
	res := events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       license,
		Headers: map[string]string{
			"Content-Type":        "text/plain",
			"Content-Disposition": "attachment; filename=\"license.txt\"",
			"Content-Length":      strconv.Itoa(length),
		},
	}

	return res, nil
}

func getLicense() (license string, length int, err error) {
	url := "https://license.tigergraph.com/api/license/free/new"
	token := "64b6614c-1f8b-448c-b790-2aecd33e2de7"

	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return
	}

	req.Header.Set("Authorization", "token "+token)

	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}

	return string(body), len(body), nil
}

func main() {
	lambda.Start(handler)
}
