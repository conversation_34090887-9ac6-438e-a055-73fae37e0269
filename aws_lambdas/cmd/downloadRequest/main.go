package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/google/uuid"
	"github.com/tigergraph/download_experience/db"
	"github.com/tigergraph/download_experience/email"
)

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	emailService, err := email.NewService("<EMAIL>", 3, time.Second)
	if err != nil {
		log.Printf("Error creating email service: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}
	cfg, _ := config.LoadDefaultConfig(ctx)
	dbService := db.New(cfg)

	var downloadRequest db.DownloadRequest
	err = json.Unmarshal([]byte(request.Body), &downloadRequest)
	if err != nil {
		log.Printf("Error unmarshalling request body: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       "Bad Request",
		}, nil
	}

	if !validateEmail(downloadRequest.UserInfo.Email) {
		log.Printf("Invalid email address: %v", downloadRequest.UserInfo.Email)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       "Bad Request",
		}, nil
	}

	requestId := uuid.New().String()
	requestTime := time.Now().Unix()

	// check if the email has been used within the last 5 minutes
	hasRecentRequests, err := dbService.HasDownloadRequestsWithin5Minutes(ctx, downloadRequest.UserInfo.Email, downloadRequest.PackageInfo)
	if err != nil {
		log.Printf("Error calling Query: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}
	if hasRecentRequests {
		return events.APIGatewayProxyResponse{
			StatusCode: 200,
			Body:       "Please be patient. We have already received your request.",
		}, nil
	}

	validateDurationHour, err := strconv.Atoi(os.Getenv("VALIDATE_DURATION"))
	if err != nil {
		log.Printf("Error parsing VALIDATE_DURATION: %v", err)
		validateDurationHour = 2
	}
	expirationTime := time.Now().Add(time.Duration(validateDurationHour) * time.Hour).Unix()
	item := db.DownloadRequestItem{
		RequestId:      requestId,
		RequestTime:    requestTime,
		ExpirationTime: expirationTime,
		Email:          downloadRequest.UserInfo.Email,
		UserInfo:       downloadRequest.UserInfo,
		PackageInfo:    downloadRequest.PackageInfo,
		Completed:      false,
	}
	// insert item to DynamoDB
	err = dbService.PutDownloadRequest(ctx, item)
	if err != nil {
		log.Printf("Error calling PutItem: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}

	// send to hubspot
	reportHubspot(downloadRequest)

	isNeo4j, err := regexp.MatchString(`(?i)@neo4j`, downloadRequest.UserInfo.Email)
	if err != nil {
		log.Printf("Error matching email: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}
	if isNeo4j {
		return events.APIGatewayProxyResponse{
			StatusCode: 200,
			Body:       "Download link sent",
		}, nil
	}

	encodedId := base64.StdEncoding.EncodeToString([]byte(requestId))
	packageURL := fmt.Sprintf("%s/package?id=%s", os.Getenv("DOWNLOAD_ENDPOINT"), encodedId)
	licenseURL := fmt.Sprintf("%s/license?id=%s", os.Getenv("DOWNLOAD_ENDPOINT"), encodedId)

	if downloadRequest.PackageInfo.Type == db.Solution {
		// Construct solution download URLs
		solutionWithDataURL := fmt.Sprintf("https://tigergraph-solution-kits-prod.s3.us-west-1.amazonaws.com/%s/4.x/solution_with_data.tar.gz", downloadRequest.PackageInfo.FileName)
		solutionWithoutDataURL := fmt.Sprintf("https://tigergraph-solution-kits-prod.s3.us-west-1.amazonaws.com/%s/4.x/solution_without_data.tar.gz", downloadRequest.PackageInfo.FileName)
		err = emailService.SendSolutionEmail(downloadRequest.PackageInfo, downloadRequest.UserInfo.FirstName, downloadRequest.UserInfo.Email, solutionWithDataURL, solutionWithoutDataURL)
	} else if downloadRequest.PackageInfo.Type == db.License {
		err = emailService.SendLicenseEmail(downloadRequest.UserInfo.FirstName, downloadRequest.UserInfo.Email, licenseURL)
	} else if downloadRequest.PackageInfo.Edition == db.CommunityEd {
		err = emailService.SendCommunityPackageEmail(downloadRequest.PackageInfo, downloadRequest.UserInfo.FirstName, downloadRequest.UserInfo.Email, packageURL)
	} else {
		err = emailService.SendPackageEmail(downloadRequest.PackageInfo, downloadRequest.UserInfo.FirstName, downloadRequest.UserInfo.Email, packageURL, licenseURL)
	}
	if err != nil {
		log.Printf("Error sending email: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Internal Server Error",
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       "Download link sent",
	}, nil
}

func validateEmail(str string) bool {
	emailPattern := `^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`
	excludesPattern := `@(gmail|hotmail|outlook|qq|163|yahoo)`
	match1, _ := regexp.MatchString(emailPattern, str)
	match2, _ := regexp.MatchString(excludesPattern, str)
	return match1 && !match2
}

func setCORS(response *events.APIGatewayProxyResponse) {
	response.Headers = make(map[string]string)
	response.Headers["Access-Control-Allow-Origin"] = "*"
	response.Headers["Access-Control-Allow-Methods"] = "POST, OPTIONS, PUT, DELETE"
	response.Headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
	response.Headers["Access-Control-Allow-Credentials"] = "true"
}

func reportHubspot(downloadRecord db.DownloadRequest) {
	url := "https://api.hsforms.com/submissions/v3/integration/submit/4114546/6ec248f0-deea-4a84-a1f3-de85e0aeb8fe"
	payload := map[string]interface{}{
		"fields": []map[string]string{
			{
				"name":  "firstname",
				"value": downloadRecord.UserInfo.FirstName,
			},
			{
				"name":  "lastname",
				"value": downloadRecord.UserInfo.LastName,
			},
			{
				"name":  "company",
				"value": downloadRecord.UserInfo.CompanyName,
			},
			{
				"name":  "phone",
				"value": downloadRecord.UserInfo.Phone,
			},
			{
				"name":  "email",
				"value": downloadRecord.UserInfo.Email,
			},
			{
				"name":  "jobtitle",
				"value": downloadRecord.UserInfo.JobCategory,
			},
			{
				"name":  "linkedin_profile",
				"value": downloadRecord.UserInfo.LinkedinProfileURL,
			},
			{
				"name":  "dl_type",
				"value": string(downloadRecord.PackageInfo.Type),
			},
			{
				"name":  "country",
				"value": downloadRecord.UserInfo.Country,
			},
			{
				"name":  "dl_version",
				"value": downloadRecord.PackageInfo.Version,
			},
			{
				"name":  "dl_file_name",
				"value": downloadRecord.PackageInfo.FileName,
			},
		},
		"context": map[string]string{
			"pageUri":  "https://dl.tigergraph.com",
			"pageName": "TigerGraph Download",
		},
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		log.Println("Error marshaling JSON payload:", err)
		return
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonPayload))
	if err != nil {
		log.Println("Error creating HTTP request:", err)
		return
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Println("Error sending HTTP request:", err)
		return
	}
	defer resp.Body.Close()

	// Process the response as needed
	log.Println("Response status:", resp.Status)
	if resp.StatusCode != 200 {
		log.Println("Response body:", resp.Body)
	} else {
		log.Println("Successfully reported to Hubspot")
	}
}

func main() {
	lambda.Start(func(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
		res, err := handler(ctx, request)
		setCORS(&res)
		return res, err
	})
}
